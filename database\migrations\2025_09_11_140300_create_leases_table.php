<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leases', function (Blueprint $table) {
            $table->id();
            $table->foreignId('property_id')->constrained('properties')->cascadeOnUpdate()->restrictOnDelete();
            $table->string('reference')->nullable()->unique();
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->date('notice_date')->nullable();
            $table->decimal('rent_amount', 10, 2);
            $table->string('rent_currency', 3)->default('EUR');
            $table->decimal('deposit_amount', 10, 2)->nullable();
            $table->string('payment_frequency', 16)->default('monthly'); // monthly, quarterly
            $table->boolean('is_active')->default(true);
            $table->json('terms')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['property_id', 'is_active']);
            $table->index('start_date');
            $table->index('end_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leases');
    }
};




