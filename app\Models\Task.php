<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Task extends Model
{
    use SoftDeletes;

    /**
     * @var list<string>
     */
    protected $fillable = [
        'project_id',
        'assigne_a',
        'parent_id',
        'titre',
        'slug',
        'image_path',
        'description',
        'statut_id',
        'categorie_id',
        'priorite',
        'ordre',
        'estimation_heures',
        'temps_passe_heures',
        'date_debut',
        'date_echeance',
        'date_terminee',
        'labels',
        'pieces_jointes',
        'recurrence',
        'created_by_id',
        'updated_by_id',
    ];

    /** @return BelongsTo<Project, Task> */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /** @return BelongsTo<User, Task> */
    public function assigneA(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigne_a');
    }
    /** @return BelongsTo<User, Task> */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_id');
    }
    /** @return BelongsTo<User, Task> */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by_id');
    }

    /** @return BelongsTo<Task, Task> */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Task::class, 'parent_id');
    }

    /** @return HasMany<Task> */
    public function children(): HasMany
    {
        return $this->hasMany(Task::class, 'parent_id');
    }
    /** @return BelongsTo<Status, Task> */
    public function status(): BelongsTo
    {
        return $this->belongsTo(Status::class, 'statut_id');
    }
    /** @return BelongsTo<Category, Task> */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'categorie_id');
    }
}
