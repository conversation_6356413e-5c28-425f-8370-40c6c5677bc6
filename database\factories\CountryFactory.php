<?php

namespace Database\Factories;

use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Country>
 */
class CountryFactory extends Factory
{
    protected $model = Country::class;

    public function definition(): array
    {
        $name = fake()->unique()->country();
        $iso2 = strtoupper(fake()->unique()->lexify('??'));
        $iso3 = strtoupper(fake()->unique()->lexify('???'));

        return [
            'name' => $name,
            'iso2' => $iso2,
            'iso3' => $iso3,
            'phone_code' => (string) fake()->numberBetween(1, 999),
        ];
    }
}




