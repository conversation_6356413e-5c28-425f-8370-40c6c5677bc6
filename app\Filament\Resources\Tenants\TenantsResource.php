<?php

namespace App\Filament\Resources\Tenants;

use App\Filament\Resources\Tenants\Pages\CreateTenants;
use App\Filament\Resources\Tenants\Pages\EditTenants;
use App\Filament\Resources\Tenants\Pages\ListTenants;
use App\Filament\Resources\Tenants\Pages\ViewTenants;
use App\Filament\Resources\Tenants\Schemas\TenantsForm;
use App\Filament\Resources\Tenants\Schemas\TenantsInfolist;
use App\Filament\Resources\Tenants\Tables\TenantsTable;
use App\Models\Tenants;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class TenantsResource extends Resource
{
    protected static ?string $model = Tenants::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $recordTitleAttribute = 'first_name';

    public static function form(Schema $schema): Schema
    {
        return TenantsForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return TenantsInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return TenantsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListTenants::route('/'),
            'create' => CreateTenants::route('/create'),
            'view' => ViewTenants::route('/{record}'),
            'edit' => EditTenants::route('/{record}/edit'),
        ];
    }
}
