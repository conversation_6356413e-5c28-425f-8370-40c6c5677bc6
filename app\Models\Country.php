<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Country extends Model
{
    use HasFactory;

    /** @var list<string> */
    protected $fillable = [
        'name',
        'iso2',
        'iso3',
        'phone_code',
    ];

    /** @return HasMany<Property> */
    public function properties(): HasMany
    {
        return $this->hasMany(Property::class);
    }

    /** @return HasMany<Tenant> */
    public function tenants(): HasMany
    {
        return $this->hasMany(Tenant::class);
    }
}




