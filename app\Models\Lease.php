<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Lease extends Model
{
    use HasFactory, SoftDeletes;

    /** @var list<string> */
    protected $fillable = [
        'property_id',
        'reference',
        'start_date',
        'end_date',
        'notice_date',
        'rent_amount',
        'rent_currency',
        'deposit_amount',
        'payment_frequency',
        'is_active',
        'terms',
    ];

    /** @return BelongsTo<Property, Lease> */
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    /** @return BelongsToMany<Tenant> */
    public function tenants(): BelongsToMany
    {
        return $this->belongsToMany(Tenant::class)
            ->withPivot(['role'])
            ->withTimestamps();
    }

    /** @return HasMany<RentPayment> */
    public function rentPayments(): HasMany
    {
        return $this->hasMany(RentPayment::class);
    }
}




