<?php

namespace App\Filament\Resources\Countries\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class CountriesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('flag')
                    ->label('')
                    ->state(fn ($record) => self::flagEmoji($record->iso2))
                    ->sortable(false)
                    ->toggleable(false),
                TextColumn::make('name')
                    ->searchable(),
                TextColumn::make('iso2')
                    ->searchable(),
                TextColumn::make('iso3')
                    ->searchable(),
                TextColumn::make('phone_code')
                    ->searchable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    private static function flagEmoji(?string $iso2): string
    {
        if (!$iso2 || strlen($iso2) !== 2) {
            return '';
        }
        $iso2 = strtoupper($iso2);
        $base = 127397; // regional indicator symbol letter A offset
        $chars = str_split($iso2);
        return mb_chr($base + ord($chars[0])) . mb_chr($base + ord($chars[1]));
    }
}
