<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Tenant extends Model
{
    use HasFactory, SoftDeletes;

    /** @var list<string> */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'birth_date',
        'national_id',
        'address_line1',
        'address_line2',
        'city',
        'state',
        'postal_code',
        'country_id',
        'metadata',
    ];

    /** @return BelongsToMany<Lease> */
    public function leases(): BelongsToMany
    {
        return $this->belongsToMany(Lease::class)
            ->withPivot(['role'])
            ->withTimestamps();
    }

    /** @return BelongsTo<Country, Tenant> */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }
}


