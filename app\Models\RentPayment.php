<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class RentPayment extends Model
{
    use HasFactory, SoftDeletes;

    /** @var list<string> */
    protected $fillable = [
        'lease_id',
        'due_date',
        'amount_due',
        'amount_paid',
        'paid_at',
        'status',
        'method',
        'reference',
        'metadata',
    ];

    /** @return BelongsTo<Lease, RentPayment> */
    public function lease(): BelongsTo
    {
        return $this->belongsTo(Lease::class);
    }
}




