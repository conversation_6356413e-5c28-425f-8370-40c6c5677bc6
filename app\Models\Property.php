<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Property extends Model
{
    use HasFactory, SoftDeletes;

    /** @var list<string> */
    protected $fillable = [
        'property_type_id',
        'reference',
        'title',
        'slug',
        'description',
        'address_line1',
        'address_line2',
        'city',
        'state',
        'postal_code',
        'country_id',
        'bedrooms',
        'bathrooms',
        'area_sqm',
        'is_active',
        'features',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'features' => 'array',
    ];

    /** @return BelongsTo<PropertyType, Property> */
    public function type(): BelongsTo
    {
        return $this->belongsTo(PropertyType::class, 'property_type_id');
    }

    /** @return BelongsTo<Country, Property> */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /** @return HasMany<Lease> */
    public function leases(): HasMany
    {
        return $this->hasMany(Lease::class);
    }
}


