<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Project extends Model
{
    use SoftDeletes;

    /**
     * @var list<string>
     */
    protected $fillable = [
        'titre',
        'client_id',
        'statut_id',
        'categorie_id',
        'description',
        'priorite',
        'progression',
        'date_debut',
        'date_fin_prevue',
        'date_fin_reelle',
        'created_by',
        'updated_by',
    ];

    /** @return BelongsTo<Client, Project> */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /** @return BelongsTo<Status, Project> */
    public function status(): BelongsTo
    {
        return $this->belongsTo(Status::class);
    }

    /** @return HasMany<Task> */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }
    /** @return BelongsTo<Category, Project> */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }
}
