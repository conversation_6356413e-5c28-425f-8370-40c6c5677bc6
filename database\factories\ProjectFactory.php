<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Client;
use App\Models\Status;
use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Project>
 */
class ProjectFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $user = User::factory()->create();
        $datebut = fake()->optional()->dateTimeBetween('-1 year', 'now');
        $datefinprevue = fake()->optional()->dateTimeBetween($datebut, '+6 months');
        $datefinreelle = fake()->optional()->dateTimeBetween($datefinprevue, '+6 months');
        return [
            'titre' => fake()->word(),
            'client_id' => Client::factory(),
            'statut_id' => Status::factory(),
            'categorie_id' => Category::factory(),
            'description' => fake()->optional()->paragraphs(2, true),
            'priorite' => fake()->randomElement([1, 2, 3, 4, 5]),
            'progression' => fake()->numberBetween(0, 100),
            'date_debut' => $datebut,
            'date_fin_prevue' => $datefinprevue,
            'date_fin_reelle' => $datefinreelle,
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ];
    }
}
