<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->word(),
            'type' => fake()->randomElement(['project', 'task']),
            'description' => fake()->optional()->paragraph(),
            'couleur_hex' => fake()->optional()->hexColor(),
            'ordre_affichage' => fake()->randomNumber(2),
            'est_actif' => fake()->boolean(),
        ];
    }
}
