<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->cascadeOnUpdate()->cascadeOnDelete();
            $table->foreignId('assigne_a')->nullable()->constrained('users')->cascadeOnUpdate()->nullOnDelete();
            $table->string('titre');
            $table->string('image_path')->nullable();
            $table->longText('description')->nullable();
            $table->unsignedTinyInteger('priorite')->default(3);
            $table->unsignedInteger('ordre')->default(0);
            $table->decimal('estimation_heures', 6, 2)->nullable();
            $table->decimal('temps_passe_heures', 8, 2)->default(0);
            $table->dateTime('date_debut')->nullable();
            $table->dateTime('date_echeance')->nullable();
            $table->dateTime('date_terminee')->nullable();
            $table->json('labels')->nullable();
            $table->json('pieces_jointes')->nullable();
            $table->json('recurrence')->nullable();
            $table->foreignId('created_by_id')->constrained('users')->cascadeOnDelete();
            $table->foreignId('updated_by_id')->nullable()->constrained('users')->cascadeOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
