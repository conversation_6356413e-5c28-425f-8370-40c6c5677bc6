<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Status;

class StatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $statuts = [
            ['code' => 'pending',     'label' => 'En attente'],
            ['code' => 'planned',     'label' => 'Prévu'],
            ['code' => 'in_progress', 'label' => 'En cours'],
            ['code' => 'completed',   'label' => 'Terminé'],
            ['code' => 'cancelled',   'label' => 'Annulé'],
        ];

        foreach ($statuts as $statut) {
            Status::updateOrCreate(
                ['code' => $statut['code']],
                ['label' => $statut['label']]
            );
        }
    }
}
