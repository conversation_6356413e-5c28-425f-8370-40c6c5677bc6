<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {

            $table->id();

            // Type de client
            $table->enum('type', ['particulier', 'entreprise'])->default('particulier');

            // Informations personnelles
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();

            // Adresse
            $table->string('address')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->default('France');

            // Statut : actif, inactif, bloqué
            $table->enum('status', ['actif', 'inactif', 'bloqué'])->default('actif');

            // Horodatage
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
