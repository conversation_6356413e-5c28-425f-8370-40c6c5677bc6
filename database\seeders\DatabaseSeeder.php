<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Lease;
use App\Models\Tenant;
use App\Models\Country;
use App\Models\Property;
use App\Models\RentPayment;
use App\Models\PropertyType;


// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Database\Seeders\CountrySeeder;
use Database\Seeders\PropertyTypeSeeder;
use Database\Seeders\PropertySeeder;
use Database\Seeders\TenantSeeder;
use Database\Seeders\LeaseSeeder;
use Database\Seeders\RentPaymentSeeder;
use Database\Seeders\StatusSeeder;
use Database\Seeders\CategorySeeder;
use Database\Seeders\ProjectSeeder;
use Database\Seeders\TaskSeeder;
use Database\Seeders\UserSeeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            CountrySeeder::class,
            PropertyTypeSeeder::class,
            PropertySeeder::class,
            TenantSeeder::class,
            LeaseSeeder::class,
            RentPaymentSeeder::class,
            StatusSeeder::class,
            CategorySeeder::class,
            ProjectSeeder::class,
            TaskSeeder::class,
        ]);
    }
}
