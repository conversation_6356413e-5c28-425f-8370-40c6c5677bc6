<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class CountrySeeder extends Seeder
{
    /**
     * php artisan db:seed --class=CountrySeeder
     */
    public function run(): void
    {
        $response = Http::withoutVerifying()->timeout(30)->get('https://restcountries.com/v3.1/all', [
            'fields' => 'cca2,cca3,name,translations,idd',
        ]);

        if ($response->successful()) {
            $countries = $response->json();

            foreach ($countries as $country) {
                $name = $country['translations']['fra']['common'] ?? ($country['name']['common'] ?? null);
                $iso2 = $country['cca2'] ?? null;
                $iso3 = $country['cca3'] ?? null;

                $phoneCode = $country['idd']['root'] ?? '';
                if (isset($country['idd']['suffixes']) && is_array($country['idd']['suffixes']) && count($country['idd']['suffixes']) > 0) {
                    $phoneCode .= $country['idd']['suffixes'][0];
                }

                if ($name && $iso2 && $iso3) {
                    DB::table('countries')->updateOrInsert(
                        ['iso2' => strtoupper($iso2)],
                        [
                            'name' => $name,
                            'iso2' => strtoupper($iso2),
                            'iso3' => strtoupper($iso3),
                            'phone_code' => $phoneCode !== '' ? $phoneCode : null,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]
                    );
                }
            }

            $this->command?->info('Countries seeded from restcountries.com');
        } else {
            $this->command?->error('Erreur lors de la récupération des pays (HTTP ' . $response->status() . ').');
        }
    }
}


