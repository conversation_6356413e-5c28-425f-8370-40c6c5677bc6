<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Status;
use App\Models\Project;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Task>
 */
class TaskFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $user = User::factory()->create();
        $datebut = fake()->optional()->dateTimeBetween('-1 year', 'now');
        $datefinprevue = fake()->optional()->dateTimeBetween($datebut, '+6 months');
        $datefinreelle = fake()->optional()->dateTimeBetween($datefinprevue, '+6 months');
        return [
            'project_id' => Project::factory(),
            'assigne_a' => $this->faker->randomElement([null, User::factory()]),
            'titre' => fake()->word(),
            'image_path' => fake()->optional()->imageUrl(640, 480),
            'description' => fake()->optional()->paragraphs(2, true),
            'statut_id' => Status::factory(),
            'created_by_id' => $user->id,
            'updated_by_id' => $user->id,
        ];
    }
}
