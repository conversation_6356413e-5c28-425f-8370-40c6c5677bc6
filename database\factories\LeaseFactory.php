<?php

namespace Database\Factories;

use App\Models\Lease;
use App\Models\Property;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends Factory<Lease>
 */
class LeaseFactory extends Factory
{
    protected $model = Lease::class;

    public function definition(): array
    {
        $start = fake()->dateTimeBetween('-2 years', 'now');
        $end = fake()->boolean(60) ? null : fake()->dateTimeBetween($start, '+2 years');

        return [
            'property_id' => Property::factory(),
            'reference' => 'LEA-' . strtoupper(Str::random(6)),
            'start_date' => $start,
            'end_date' => $end,
            'notice_date' => null,
            'rent_amount' => fake()->randomFloat(2, 300, 2500),
            'rent_currency' => 'EUR',
            'deposit_amount' => fake()->optional()->randomFloat(2, 300, 2500),
            'payment_frequency' => 'monthly',
            'is_active' => $end === null || $end > now(),
            'terms' => null,
        ];
    }
}




