<?php

namespace Database\Factories;

use App\Models\Property;
use App\Models\PropertyType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends Factory<Property>
 */
class PropertyFactory extends Factory
{
    protected $model = Property::class;

    public function definition(): array
    {
        $title = fake()->streetName() . ' ' . fake()->randomElement(['Maison', 'Appartement', 'Villa']);
        return [
            'property_type_id' => PropertyType::factory(),
            'reference' => strtoupper(Str::random(6)),
            'title' => $title,
            'slug' => Str::slug($title . '-' . fake()->unique()->numerify('###')),
            'description' => fake()->optional()->paragraphs(2, true),
            'address_line1' => fake()->streetAddress(),
            'address_line2' => fake()->optional()->secondaryAddress(),
            'city' => fake()->city(),
            'state' => fake()->optional()->state(),
            'postal_code' => fake()->postcode(),
            'country_id' => null,
            'bedrooms' => fake()->numberBetween(0, 5),
            'bathrooms' => fake()->numberBetween(0, 3),
            'area_sqm' => fake()->numberBetween(18, 250),
            'is_active' => true,
            'features' => [
                'parking' => fake()->boolean(),
                'balcony' => fake()->boolean(),
                'furnished' => fake()->boolean(),
            ],
        ];
    }
}


