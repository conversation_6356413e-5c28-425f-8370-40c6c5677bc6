<?php

namespace Database\Factories;

use App\Models\PropertyType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends Factory<PropertyType>
 */
class PropertyTypeFactory extends Factory
{
    protected $model = PropertyType::class;

    public function definition(): array
    {
        $name = fake()->randomElement(['Maison', 'Appartement', 'Studio', 'Villa', 'Loft', 'Duplex']);

        return [
            'name' => $name,
            'slug' => Str::slug($name . '-' . fake()->unique()->numerify('###')),
            'description' => fake()->optional()->paragraph(),
        ];
    }
}




