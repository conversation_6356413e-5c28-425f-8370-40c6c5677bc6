<?php

namespace Database\Factories;

use App\Models\Lease;
use App\Models\RentPayment;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<RentPayment>
 */
class RentPaymentFactory extends Factory
{
    protected $model = RentPayment::class;

    public function definition(): array
    {
        $due = fake()->dateTimeBetween('-1 year', '+6 months');
        $status = fake()->randomElement(['pending', 'paid', 'partial', 'overdue']);
        $amountDue = fake()->randomFloat(2, 300, 2000);
        $amountPaid = match ($status) {
            'paid' => $amountDue,
            'partial' => max(0, $amountDue - fake()->randomFloat(2, 10, $amountDue - 1)),
            default => 0,
        };

        return [
            'lease_id' => Lease::factory(),
            'due_date' => $due,
            'amount_due' => $amountDue,
            'amount_paid' => $amountPaid,
            'paid_at' => in_array($status, ['paid', 'partial'], true)
                ? fake()->dateTimeBetween((clone $due)->modify('-10 days'), (clone $due)->modify('+20 days'))
                : null,
            'status' => $status,
            'method' => fake()->optional()->randomElement(['bank_transfer', 'cash', 'card', 'check']),
            'reference' => fake()->optional()->bothify('PAY-####'),
            'metadata' => null,
        ];
    }
}


